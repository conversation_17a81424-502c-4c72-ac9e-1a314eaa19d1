# CSS Organization Structure

This document describes the organized CSS structure for the AureaVoice frontend application.

## File Structure

```
src/
├── style.css          # Main entry point - imports all CSS modules
└── styles/
    ├── base.css           # Core reset and typography
    ├── layout.css         # Container and positioning styles
    ├── components.css     # Buttons, microphone, and UI elements
    ├── animations.css     # Functional animations for recording states
    ├── responsive.css     # Media queries for different screen sizes
    └── README.md          # This documentation file
```

## Import Order

The CSS files are imported in a specific order to ensure proper cascading:

1. **base.css** - Reset and core styles (must be first)
2. **layout.css** - Container and positioning
3. **components.css** - Buttons and UI elements
4. **animations.css** - Functional animations for recording states
5. **responsive.css** - Media queries (should be last)

## File Contents

### base.css
- CSS reset (`*, *::before, *::after`)
- Body typography and background
- App container base styles

### layout.css
- Container layouts and positioning
- Welcome and test page text styles

### components.css
- Microphone button styles (both center and floating)
- Recording states (recording, loading)
- Recording duration display
- Microphone icon styles

### animations.css
- Recording pulse animation for microphone recording state
- Loading spin animation for processing state

### responsive.css
- Media queries for tablet (768px and below)
- Media queries for mobile (480px and below)
- Responsive typography and spacing adjustments

## Usage

To use the organized CSS structure, simply import the main style.css file in your HTML:

```html
<link rel="stylesheet" href="/src/style.css">
```

The style.css file will automatically import all other CSS files in the correct order.

## Benefits

1. **Logical Organization**: Each file has a clear, specific purpose
2. **Maintainability**: Easy to find and modify specific styles
3. **Modularity**: Can easily add/remove specific style modules
4. **Performance**: Browser can cache individual files efficiently
5. **Collaboration**: Multiple developers can work on different style aspects
6. **Debugging**: Easier to identify which file contains specific styles

## Guidelines

- Keep base.css minimal - only reset and core typography
- Put layout-related styles in layout.css
- Component-specific styles go in components.css
- Functional animations (recording, loading) belong in animations.css
- Media queries should be in responsive.css and loaded last
