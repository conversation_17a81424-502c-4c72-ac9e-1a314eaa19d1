
.microphone-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #4299e1;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
  opacity: 1;
  /* Remove conflicting transitions for View Transition API */
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
  /* Optimize for View Transitions */
  contain: layout style paint;
}

.microphone-button:hover {
  background-color: #3182ce;
  box-shadow: 0 6px 16px rgba(66, 153, 225, 0.4);
  /* Remove transform to avoid conflicts with View Transitions */
}

/* Floating microphone styles */
.floating-microphone {
  position: fixed;
  bottom: 300px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #4299e1;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
  opacity: 1;
  /* Remove conflicting transitions for View Transition API */
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
  /* Optimize for View Transitions */
  contain: layout style paint;
}

.floating-microphone:hover {
  background-color: #3182ce;
  box-shadow: 0 6px 16px rgba(66, 153, 225, 0.4);
  /* Keep only translateX, remove scale to avoid conflicts */
  transform: translateX(-50%);
}

/* Processing state for microphone */
.floating-microphone.processing {
  background-color: #ed8936;
  cursor: not-allowed;
}

.floating-microphone.processing:hover {
  background-color: #ed8936;
  transform: translateX(-50%);
  box-shadow: 0 4px 12px rgba(237, 137, 54, 0.3);
}

/* Processing spinner */
.processing-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Try again button styles */
.try-again-button {
  padding: 12px 32px;
  border-radius: 8px;
  background-color: #4299e1;
  border: none;
  cursor: pointer;
  color: white;
  font-size: 1.1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
  opacity: 1;
}

.try-again-button:hover {
  background-color: #3182ce;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(66, 153, 225, 0.4);
}



.floating-microphone.recording {
  background-color: #ff6b6b;
  animation: recording-pulse 1.5s ease-in-out infinite;
  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.6);
}

.floating-microphone.recording:hover {
  background-color: #ee5a24;
  transform: translateX(-50%) scale(1.05);
  box-shadow: 0 6px 25px rgba(255, 107, 107, 0.8);
}

.floating-microphone.loading {
  background-color: #ffa500;
  animation: loading-spin 1s linear infinite;
  cursor: not-allowed;
  opacity: 0.8;
}

.floating-microphone.loading:hover {
  background-color: #ffa500;
  transform: translateX(-50%) scale(1);
}

/* Microphone icon styles */
.microphone-icon {
  width: 24px;
  height: 24px;
  fill: currentColor;
  transition: fill 0.3s ease;
}

/* Transitioning state styles for View Transition API */
.microphone-button.transitioning,
.floating-microphone.transitioning {
  z-index: 1000;
}

/* Fallback styles for browsers without View Transition API */
.no-view-transitions .microphone-button:hover {
  transform: scale(1.02);
}

.no-view-transitions .floating-microphone:hover {
  transform: translateX(-50%) scale(1.02);
}

.no-view-transitions .microphone-button:active {
  transform: scale(0.98);
}

.no-view-transitions .floating-microphone:active {
  transform: translateX(-50%) scale(0.98);
}



/* Recording duration display */
.recording-duration {
  position: fixed;
  bottom: 400px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  font-weight: bold;
  z-index: 1000;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}



